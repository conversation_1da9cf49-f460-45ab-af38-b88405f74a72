import os
from celery import Celery
from celery.schedules import crontab

# Establecer la variable de entorno para configuraciones de Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_project.settings')

# Crear instancia de Celery
app = Celery('inventory_project')

# Cargar configuración desde settings.py
app.config_from_object('django.conf:settings', namespace='CELERY')

# Descubrir tareas automáticamente
app.autodiscover_tasks()

# Definir tareas periódicas
app.conf.beat_schedule = {
    'crear-hoja-diaria': {
        'task': 'inventory.tasks.crear_hoja_diaria',
        'schedule': crontab(hour=0, minute=5),  # Ejecutar a las 00:05 cada día
    },
    'consolidar-almacen': {
        'task': 'inventory.tasks.consolidar_almacen',
        'schedule': crontab(hour='*/2'),  # Ejecutar cada 2 horas
    },
}

@app.task(bind=True, ignore_result=True)
def debug_task(self):
    print(f'Request: {self.request!r}')