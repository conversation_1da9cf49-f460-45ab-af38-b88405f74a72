from django.db import models
from django.db.models import Sum, F
from decimal import Decimal

class Template(models.Model):
    """Modelo equivalente a la hoja 'Plantilla' del Excel"""
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Plantilla {self.id}"

class DailyRecord(models.Model):
    """Modelo para registros diarios (equivalente a hojas diarias)"""
    date = models.DateField()
    item = models.CharField(max_length=100)
    quedan = models.IntegerField(default=0)
    entrega1 = models.IntegerField(default=0)
    recogida1 = models.IntegerField(default=0)
    venta1 = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    precio_venta = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    class Meta:
        unique_together = ['date', 'item']
    
    @property
    def calculo_inventario(self):
        """Equivalente a =Tabla[[#This Row],[Queda]] + 50"""
        return self.quedan + 50
    
    @property
    def balance_entrega_recogida(self):
        """Equivalente a =Tabla[[#This Row],[entrega 1]] - Tabla[[#This Row],[recogida 1]]"""
        return self.entrega1 - self.recogida1
    
    @property
    def total_venta(self):
        """Equivalente a =Tabla[[#This Row],[Venta]] * Tabla[[#This Row],[precio de venta]]"""
        return self.venta1 * self.precio_venta
    
    def __str__(self):
        return f"{self.date} - {self.item}"

class Inventory(models.Model):
    """Modelo equivalente a la hoja 'Almacen'"""
    item = models.CharField(max_length=100, unique=True)
    stock_actual = models.IntegerField(default=0)
    last_updated = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.item} - Stock: {self.stock_actual}"

class Consolidation(models.Model):
    """Modelo para seguimiento de sincronización entre registros diarios y almacén"""
    daily_record = models.ForeignKey(DailyRecord, on_delete=models.CASCADE)
    inventory = models.ForeignKey(Inventory, on_delete=models.CASCADE)
    synced_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['daily_record', 'inventory']