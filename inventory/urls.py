from django.urls import path
from . import views

urlpatterns = [
    path('', views.DashboardView.as_view(), name='dashboard'),
    path('daily/', views.DailyRecordListView.as_view(), name='daily_list'),
    path('daily/<str:date>/', views.DailyRecordListView.as_view(), name='daily_list_date'),
    path('daily/add/', views.DailyRecordCreateView.as_view(), name='daily_add'),
    path('daily/edit/<int:pk>/', views.DailyRecordUpdateView.as_view(), name='daily_edit'),
    path('inventory/', views.InventoryListView.as_view(), name='inventory_list'),
]