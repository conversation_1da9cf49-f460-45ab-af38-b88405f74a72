{% extends 'base.html' %}

{% block title %}Inventario - Sistema de Inventario{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">Inventario General</h1>
        <div>
            <button id="exportBtn" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded mr-2">
                Exportar CSV
            </button>
            <button id="refreshBtn" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Actualizar Stock
            </button>
        </div>
    </div>
    
    <!-- Filtro de búsqueda -->
    <div class="mb-6">
        <div class="relative">
            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
            </div>
            <input type="text" id="searchInput" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5" placeholder="Buscar por nombre de ítem...">
        </div>
    </div>
    
    <!-- Tabla de inventario -->
    <div class="bg-gray-800 shadow-md rounded my-6 overflow-hidden">
        <table id="inventoryTable" class="min-w-full bg-gray-800">
            <thead>
                <tr class="bg-gray-700 text-gray-300 uppercase text-sm leading-normal">
                    <th class="py-3 px-6 text-left cursor-pointer" onclick="sortTable(0)">Ítem ↕</th>
                    <th class="py-3 px-6 text-center cursor-pointer" onclick="sortTable(1)">Stock Actual ↕</th>
                    <th class="py-3 px-6 text-center">Última Actualización</th>
                    <th class="py-3 px-6 text-center">Estado</th>
                </tr>
            </thead>
            <tbody class="text-gray-400 text-sm">
                {% for item in items %}
                <tr class="border-b border-gray-700 hover:bg-gray-700">
                    <td class="py-3 px-6 text-left whitespace-nowrap">{{ item.item }}</td>
                    <td class="py-3 px-6 text-center">{{ item.stock_actual }}</td>
                    <td class="py-3 px-6 text-center">{{ item.last_updated|date:"d-m-Y H:i" }}</td>
                    <td class="py-3 px-6 text-center">
                        {% if item.stock_actual <= 5 %}
                        <span class="bg-red-900 text-red-300 py-1 px-3 rounded-full text-xs">Crítico</span>
                        {% elif item.stock_actual <= 15 %}
                        <span class="bg-yellow-900 text-yellow-300 py-1 px-3 rounded-full text-xs">Bajo</span>
                        {% else %}
                        <span class="bg-green-900 text-green-300 py-1 px-3 rounded-full text-xs">Normal</span>
                        {% endif %}
                    </td>
                </tr>
                {% empty %}
                <tr class="border-b border-gray-700">
                    <td colspan="4" class="py-