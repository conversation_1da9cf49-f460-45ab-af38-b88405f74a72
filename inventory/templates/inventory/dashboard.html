{% extends 'base.html' %}

{% block title %}Dashboard - Sistema de Inventario{% endblock %}

{% block content %}
<div class="container mx-auto px-4">
    <h1 class="text-3xl font-bold mb-6">Dashboard</h1>
    
    <!-- Tarjetas de resumen -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-gray-800 rounded-lg shadow-md p-6 border border-gray-700">
            <h2 class="text-xl font-semibold mb-2">Total Items</h2>
            <p class="text-4xl font-bold text-primary-400">{{ inventory_count }}</p>
        </div>
        <div class="bg-gray-800 rounded-lg shadow-md p-6 border border-gray-700">
            <h2 class="text-xl font-semibold mb-2">Ventas Hoy</h2>
            <p class="text-4xl font-bold text-green-500">{{ ventas_hoy }} €</p>
        </div>
        <div class="bg-gray-800 rounded-lg shadow-md p-6 border border-gray-700">
            <h2 class="text-xl font-semibold mb-2">Ventas Mes</h2>
            <p class="text-4xl font-bold text-blue-500">{{ ventas_mes }} €</p>
        </div>
    </div>
    
    <!-- Gráfico de ventas -->
    <div class="bg-gray-800 rounded-lg shadow-md p-6 border border-gray-700 mb-8">
        <h2 class="text-xl font-semibold mb-4">Ventas últimos 30 días</h2>
        <div class="h-80">
            <canvas id="ventasChart"></canvas>
        </div>
    </div>
    
    <!-- Tabla de productos con bajo stock -->
    <div class="bg-gray-800 rounded-lg shadow-md p-6 border border-gray-700">
        <h2 class="text-xl font-semibold mb-4">Productos con bajo stock</h2>
        <div class="overflow-x-auto">
            <table class="min-w-full bg-gray-800">
                <thead class="bg-gray-700 text-gray-300">
                    <tr>
                        <th class="py-3 px-6 text-left">Ítem</th>
                        <th class="py-3 px-6 text-center">Stock Actual</th>
                        <th class="py-3 px-6 text-center">Estado</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in low_stock %}
                    <tr class="border-b border-gray-700 hover:bg-gray-700">
                        <td class="py-3 px-6 text-left">{{ item.item }}</td>
                        <td class="py-3 px-6 text-center">{{ item.stock_actual }}</td>
                        <td class="py-3 px-6 text-center">
                            {% if item.stock_actual <= 5 %}
                            <span class="bg-red-900 text-red-300 py-1 px-3 rounded-full text-xs">Crítico</span>
                            {% else %}
                            <span class="bg-yellow-900 text-yellow-300 py-1 px-3 rounded-full text-xs">Bajo</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% empty %}
                    <tr class="border-b border-gray-700">
                        <td colspan="3" class="py-3 px-6 text-center">No hay productos con bajo stock</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Datos para el gráfico de ventas
    const ventasData = {
        labels: [{% for venta in ventas_por_dia %}'{{ venta.date|date:"d/m" }}',{% endfor %}],
        datasets: [{
            label: 'Ventas (€)',
            data: [{% for venta in ventas_por_dia %}{{ venta.total }},{% endfor %}],
            backgroundColor: 'rgba(14, 165, 233, 0.2)',
            borderColor: 'rgba(14, 165, 233, 1)',
            borderWidth: 2,
            tension: 0.4,
            fill: true
        }]
    };

    // Configuración del gráfico
    const ventasConfig = {
        type: 'line',
        data: ventasData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.7)'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.7)'
                    }
                }
            },
            plugins: {
                legend: {
                    labels: {
                        color: 'rgba(255, 255, 255, 0.7)'
                    }
                }
            }
        }
    };

    // Crear el gráfico
    const ventasChart = new Chart(
        document.getElementById('ventasChart'),
        ventasConfig
    );
</script>
{% endblock %}