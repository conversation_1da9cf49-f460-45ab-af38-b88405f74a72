{% extends 'base.html' %}

{% block title %}
    {% if form.instance.id %}
        Editar Registro - Sistema de Inventario
    {% else %}
        Nuevo Registro - Sistema de Inventario
    {% endif %}
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">
            {% if form.instance.id %}
                Editar Registro
            {% else %}
                Nuevo Registro Diario
            {% endif %}
        </h1>
        <a href="{% url 'daily_list' %}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Volver
        </a>
    </div>
    
    <div class="bg-gray-800 shadow-md rounded p-6 border border-gray-700">
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            {% if form.non_field_errors %}
            <div class="bg-red-900 text-red-200 p-4 rounded mb-4">
                {% for error in form.non_field_errors %}
                    <p>{{ error }}</p>
                {% endfor %}
            </div>
            {% endif %}
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Fecha -->
                <div>
                    <label for="{{ form.date.id_for_label }}" class="block text-sm font-medium text-gray-300 mb-2">
                        Fecha
                    </label>
                    {{ form.date.errors }}
                    <input type="date" name="{{ form.date.name }}" id="{{ form.date.id_for_label }}" 
                           value="{{ form.date.value|date:'Y-m-d'|default:'' }}"
                           class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                </div>
                
                <!-- Ítem -->
                <div>
                    <label for="{{ form.item.id_for_label }}" class="block text-sm font-medium text-gray-300 mb-2">
                        Ítem
                    </label>
                    {{ form.item.errors }}
                    <input type="text" name="{{ form.item.name }}" id="{{ form.item.id_for_label }}" 
                           value="{{ form.item.value|default:'' }}"
                           class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                </div>
                
                <!-- Quedan -->
                <div>
                    <label for="{{ form.quedan.id_for_label }}" class="block text-sm font-medium text-gray-300 mb-2">
                        Quedan
                    </label>
                    {{ form.quedan.errors }}
                    <input type="number" name="{{ form.quedan.name }}" id="{{ form.quedan.id_for_label }}" 
                           value="{{ form.quedan.value|default:'0' }}"
                           class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                </div>
                
                <!-- Entrega -->
                <div>
                    <label for="{{ form.entrega1.id_for_label }}" class="block text-sm font-medium text-gray-300 mb-2">
                        Entrega
                    </label>
                    {{ form.entrega1.errors }}
                    <input type="number" name="{{ form.entrega1.name }}" id="{{ form.entrega1.id_for_label }}" 
                           value="{{ form.entrega1.value|default:'0' }}"
                           class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                </div>
                
                <!-- Recogida -->
                <div>
                    <label for="{{ form.recogida1.id_for_label }}" class="block text-sm font-medium text-gray-300 mb-2">
                        Recogida
                    </label>
                    {{ form.recogida1.errors }}
                    <input type="number" name="{{ form.recogida1.name }}" id="{{ form.recogida1.id_for_label }}" 
                           value="{{ form.recogida1.value|default:'0' }}"
                           class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                </div>
                
                <!-- Venta -->
                <div>
                    <label for="{{ form.venta1.id_for_label }}" class="block text-sm font-medium text-gray-300 mb-2">
                        Venta
                    </label>
                    {{ form.venta1.errors }}
                    <input type="number" step="0.01" name="{{ form.venta1.name }}" id="{{ form.venta1.id_for_label }}" 
                           value="{{ form.venta1.value|default:'0.00' }}"
                           class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                </div>
                
                <!-- Precio de venta -->
                <div>
                    <label for="{{ form.precio_venta.id_for_label }}" class="block text-sm font-medium text-gray-300 mb-2">
                        Precio de venta (€)
                    </label>
                    {{ form.precio_venta.errors }}
                    <input type="number" step="0.01" name="{{ form.precio_venta.name }}" id="{{ form.precio_venta.id_for_label }}" 
                           value="{{ form.precio_venta.value|default:'0.00' }}"
                           class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                </div>
            </div>
            
            <div class="flex justify-end">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    {% if form.instance.id %}
                        Actualizar
                    {% else %}
                        Guardar
                    {% endif %}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}