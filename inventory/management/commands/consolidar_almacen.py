from django.core.management.base import BaseCommand
from django.utils import timezone
from inventory.models import DailyRecord, Inventory, Consolidation
from django.db.models import F

class Command(BaseCommand):
    help = 'Consolida los registros diarios en el inventario'

    def handle(self, *args, **options):
        # Obtener registros diarios no sincronizados
        daily_records = DailyRecord.objects.exclude(
            id__in=Consolidation.objects.values_list('daily_record_id', flat=True)
        )
        
        records_synced = 0
        for record in daily_records:
            # Actualizar o crear item en inventario
            inventory, created = Inventory.objects.update_or_create(
                item=record.item,
                defaults={
                    'stock_actual': record.quedan + record.entrega1 - record.recogida1
                }
            )
            
            # Registrar la sincronización
            Consolidation.objects.create(
                daily_record=record,
                inventory=inventory
            )
            
            records_synced += 1
        
        self.stdout.write(
            self.style.SUCCESS(f'Se sincronizaron {records_synced} registros con el inventario')
        )