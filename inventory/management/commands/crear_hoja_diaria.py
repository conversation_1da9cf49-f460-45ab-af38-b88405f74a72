from django.core.management.base import BaseCommand
from django.utils import timezone
from inventory.models import DailyRecord, Inventory
import datetime

class Command(BaseCommand):
    help = 'Crea registros diarios basados en la plantilla'

    def handle(self, *args, **options):
        today = timezone.now().date()
        
        # Obtener todos los items del inventario
        inventory_items = Inventory.objects.all()
        
        # Crear registros diarios para cada item
        records_created = 0
        for item in inventory_items:
            # Verificar si ya existe un registro para hoy
            if not DailyRecord.objects.filter(date=today, item=item.item).exists():
                DailyRecord.objects.create(
                    date=today,
                    item=item.item,
                    quedan=item.stock_actual,  # Inicializar con el stock actual
                )
                records_created += 1
        
        self.stdout.write(
            self.style.SUCCESS(f'Se crearon {records_created} registros para {today}')
        )