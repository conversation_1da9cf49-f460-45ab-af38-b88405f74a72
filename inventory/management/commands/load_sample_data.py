from django.core.management.base import BaseCommand
from django.utils import timezone
from inventory.models import Inventory, DailyRecord
import datetime
import random

class Command(BaseCommand):
    help = 'Carga datos de ejemplo para probar la aplicación'

    def handle(self, *args, **options):
        # Crear items de inventario
        inventory_items = [
            {'item': 'Camiseta Básica', 'stock_actual': 25},
            {'item': 'Pantalón Vaquero', 'stock_actual': 18},
            {'item': 'Zapatillas Deportivas', 'stock_actual': 12},
            {'item': 'Chaqueta de Cuero', 'stock_actual': 8},
            {'item': 'Bufanda de Lana', 'stock_actual': 30},
            {'item': 'Gorra de Béisbol', 'stock_actual': 22},
            {'item': 'Calcetines Pack 3', 'stock_actual': 45},
            {'item': 'Cinturón de Cuero', 'stock_actual': 15},
            {'item': 'Reloj Analógico', 'stock_actual': 5},
            {'item': '<PERSON><PERSON><PERSON> de Sol', 'stock_actual': 10},
            {'item': '<PERSON><PERSON><PERSON> de Mano', 'stock_actual': 7},
            {'item': '<PERSON><PERSON> de Piel', 'stock_actual': 20},
        ]
        
        # Crear o actualizar items de inventario
        for item_data in inventory_items:
            Inventory.objects.update_or_create(
                item=item_data['item'],
                defaults={'stock_actual': item_data['stock_actual']}
            )
        
        self.stdout.write(self.style.SUCCESS(f'Creados {len(inventory_items)} items de inventario'))
        
        # Crear registros diarios para los últimos 30 días
        today = timezone.now().date()
        
        # Precios de venta para cada item
        prices = {
            'Camiseta Básica': 15.99,
            'Pantalón Vaquero': 39.99,
            'Zapatillas Deportivas': 59.99,
            'Chaqueta de Cuero': 89.99,
            'Bufanda de Lana': 19.99,
            'Gorra de Béisbol': 14.99,
            'Calcetines Pack 3': 9.99,
            'Cinturón de Cuero': 24.99,
            'Reloj Analógico': 49.99,
            'Gafas de Sol': 29.99,
            'Bolso de Mano': 34.99,
            'Cartera de Piel': 19.99,
        }
        
        daily